import profileSlice from "@/apps/Account/Pages/Profile/ClientSideStates";
import autoDialerSlice from "@/apps/Admin/Pages/AutoDialer/ClientSideStates";
import classificationSlice from "@/apps/Admin/Pages/Classification/ClientSideStates";
import customerSlice from "@/apps/Admin/Pages/Customers/ClientSideStates";
import departmentSlice from "@/apps/Admin/Pages/Department/ClientSideStates";
import auditLogSlice from "@/apps/Admin/Pages/AuditLogs/ClientSideStates";
import notesSlice from "@/apps/Admin/Pages/Notes/ClientSideStates";
import notificationWaySlice from "@/apps/Admin/Pages/NotificationWay/ClientSideStates";
import professionSlice from "@/apps/Admin/Pages/Profession/ClientSideStates";
import recordingsSlice from "@/apps/Admin/Pages/Recordings/ClientSideStates";
import roleSlice from "@/apps/Admin/Pages/Role/ClientSideStates";
import sectorSlice from "@/apps/Admin/Pages/Sector/ClientSideStates";
import taskSlice from "@/apps/Admin/Pages/Task/ClientSideStates";
import ticketSlice from "@/apps/Admin/Pages/Ticket/ClientSideStates";
import subjectTicketSlice from "@/apps/Admin/Pages/TicketSubject/ClientSideStates";
import userSlice from "@/apps/Admin/Pages/Users/<USER>";
import callSlice from "@/apps/Call/ClientSideStates";
import chatSlice from "@/apps/Chat/ClientSideStates";
import dashboardSlice from "@/apps/Dashboard/ClientStates";
import languageSlice from "@/apps/Language/ClientSideStates";
import notificationSlice from "@/apps/Notification/ClientSideStates";
import pauseSlice from "@/apps/Pauses/ClientSideStates";
import teamSlice from "@/apps/Team/ClientStates";
import { combineReducers } from "@reduxjs/toolkit";
import PauseTypeSlice from "@/apps/Admin/Pages/PauseType/ClientSideStates";
import customerSourceSlice from "@/apps/Admin/Pages/CustomerSource/ClientSideStates";
import threecxqueues from "@/apps/Admin/Pages/ThreeCXQueues/ClientSideStates"
import folderSlice from "@/apps/FileManager/ClientSideStates";
import workFlowSlice from "@/apps/Admin/Pages/WorkFlow/ClientSideStates";

const rootReducer = combineReducers({
  users: userSlice.reducer,
  customer: customerSlice.reducer,
  profile: profileSlice.reducer,
  recordings: recordingsSlice.reducer,
  pause: pauseSlice.reducer,
  department: departmentSlice.reducer,
  notification: notificationSlice.reducer,
  sector: sectorSlice.reducer,
  profession: professionSlice.reducer,
  subjectTicket: subjectTicketSlice.reducer,
  ticket: ticketSlice.reducer,
  language: languageSlice.reducer,
  chat: chatSlice.reducer,
  autoDialer: autoDialerSlice.reducer,
  task: taskSlice.reducer,
  notes: notesSlice.reducer,
  team: teamSlice.reducer,
  dashboard: dashboardSlice.reducer,
  classification: classificationSlice.reducer,
  threecxqueues: threecxqueues.reducer,
  role: roleSlice.reducer,
  call: callSlice.reducer,
  notificationWay: notificationWaySlice.reducer,
  auditLog: auditLogSlice.reducer,
  pauseType: PauseTypeSlice.reducer,
  customerSource: customerSourceSlice.reducer,
  folder: folderSlice.reducer,
  workFlow:workFlowSlice.reducer,
});

export type RootState = ReturnType<typeof rootReducer>;

export default rootReducer;
