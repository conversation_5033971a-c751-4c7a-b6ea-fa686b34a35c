import { FC, memo } from 'react';
import { Handle, Position, NodeResizer } from '@xyflow/react';

const ResizableNodeSelected: FC<any> = ({ data, selected }) => {
  return (
    <>
      <NodeResizer
        color="#0096d1"
        isVisible={selected}
        minWidth={100}
        minHeight={30}
      />

      {/* TARGET HANDLES */}
      <Handle type="target" position={Position.Top} id="target-top"  />
      <Handle type="target" position={Position.Right} id="target-right" />
      <Handle type="target" position={Position.Bottom} id="target-bottom"  />
      <Handle type="target" position={Position.Left} id="target-left" />

      <div style={{ padding: 10 }}>{data.label}</div>

      {/* SOURCE HANDLES */}
      <Handle type="source" position={Position.Top} id="source-top"  />
      <Handle type="source" position={Position.Right} id="source-right"  />
      <Handle type="source" position={Position.Bottom} id="source-bottom" />
      <Handle type="source" position={Position.Left} id="source-left"  />
    </>
  );
};

export default memo(ResizableNodeSelected);
