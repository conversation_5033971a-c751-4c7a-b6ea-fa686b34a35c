import PageTitle from "@/apps/Common/PageTitle";
import { <PERSON>, <PERSON>vide<PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";

import { useNavigate, useSearchParams } from "react-router-dom";
import EdgeTypesFlow from "./Flow";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { useTranslation } from "react-i18next";
import AddOrUpdateNodeButton from "./Nodes/AddOrUpdateNodeButton";
import AddOrUpdateEdgeButton from "./Edges/AddOrUpdateEdgeButton";

const AddOrUpdateWorkflowIndex = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams()
  return (
    <>
      <Col xs={24}>
        <Row className="!px-2">
          <Col xs={24} className="">
            <div className="!flex gap-1 items-center !p-2">
              <ArrowLeftOutlined
                onClick={() => {
                  queryClient.resetQueries({
                    queryKey: endPoints.getWorkFlowListFilter,
                    exact: false,
                  });
                  navigate(-1);
                
                }}
                className="!text-xl !text-gray-400"
              />

              <PageTitle title={t("workFlow.editWorkFlow")} isSubTitle />
              <span>({searchParams.get("flowName")||""})</span>

              
            </div>
          </Col>
          <Col xs={24}>
            <Divider className="!m-0" />
          </Col>
        
          <Col xs={24}>
              <EdgeTypesFlow />
          
          </Col>
        </Row>
      </Col>
      
    </>
  );
};

export default AddOrUpdateWorkflowIndex;
