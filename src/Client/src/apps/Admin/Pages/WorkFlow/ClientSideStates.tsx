import { createSlice } from "@reduxjs/toolkit";

const InitialState: {filter:any,} = {
 
  filter: {
    PageNumber: 1,
    PageSize: 30,
  },
};

const workFlowSlice = createSlice({
  name: "WorkFlowSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetWorkFlowFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
   
   
  
    handleResetAllFieldsWorkFlow: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterWorkFlow: (state) => {
       state.filter = {
        PageNumber: 1,
        PageSize: 30,
      }
      },
  },
});

export const { handleResetAllFieldsWorkFlow,handleResetFilterWorkFlow,hanldleSetWorkFlowFilter } = workFlowSlice.actions;
export default workFlowSlice;
