import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import {
  getEdgeRuleListFilter,
  getEdgeRuleTypeList,
  getNodeEdgeListFilter,
  getWorkFlowListFilter,
  getWorkFlowNodeListFilter,
} from "./Services";

export const useGetWorkFlows = (filter?: any) => {
  const query = useQuery(
    [endpoints.getWorkFlowListFilter, filter],
    () => {
      return getWorkFlowListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetWorkFlowNodes = (filter?: any) => {
  const query = useQuery(
    [endpoints.getWorkFlowNodes, filter],
    () => {
      return getWorkFlowNodeListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetNodeEdges = (filter?: any) => {
  const query = useQuery(
    [endpoints.getNodeEdgeList, filter],
    () => {
      return getNodeEdgeListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetEdgeRules = (filter?: any) => {
  const query = useQuery(
    [endpoints.getEdgeRulesList, filter],
    () => {
      return getEdgeRuleListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetEdgeRuleTypes = () => {
  const query = useQuery(
    [endpoints.getRuleTypeList],
    () => {
      return getEdgeRuleTypeList();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
