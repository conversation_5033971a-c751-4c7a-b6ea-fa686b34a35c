import { FC } from "react";
import { GeneralSelectInputs } from "@/models/Client/GeneralSelectInputs";
import { normalizeString } from "@/helpers/TRNormalizedName";
import { MazakaSelect } from "./MazakaSelect";
import { useGetCustomerClassifications } from "../Admin/Pages/Customers/ServerSideStates";
import { useGetWorkFlowNodes } from "../Admin/Pages/WorkFlow/ServerSideStates";

const GeneralNodes: FC<GeneralSelectInputs> = (props) => {
  const nodes = useGetWorkFlowNodes({
    PageSize: 100,
    FlowId: props.externalValueId,
  });
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        tooltip={props.tooltip}
        loading={nodes.isLoading || nodes.isFetching}
        disabled={nodes.isLoading || nodes.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(
            option.label.toLowerCase().trim()
          );
          const status = (normalizedLabel ?? "").includes(
            normalizedInput.toLowerCase()
          );
          return status;
        }}
        label={props.label}
        options={
          nodes.data?.Value
            ? nodes.data?.Value?.filter((node:any)=>{
              return !props.excludeIds?.includes(node.Id)
            })?.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Name,
                  flowId: item.FlowId,
                  type: item.Type,
                  typeId: item.TypeId,
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralNodes;
