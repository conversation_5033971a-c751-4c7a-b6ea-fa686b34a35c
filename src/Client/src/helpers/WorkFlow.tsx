export const determineNodeStatus = (
    type: "color" | "value"|"select",
    typeId: number|null,
    t:any
  ) => {
    if (type === "color") {
      switch (typeId) {
       
        case 1:
          return "!bg-[#d1d5db]";
        case 2:
          return "!bg-[#0096d1]";
        case 3:
          return "!bg-[#35b214]";
      }
    } 
    else if(type==="select")
    {
      return [
        // {label:t("workFlow.start"),value:1},
        {label:t("workFlow.process"),value:2},
        {label:t("workFlow.end"),value:3},
      ]
    }
    else {
      switch (typeId) {
       
        case 1:
          return t("workFlow.start");
        case 2:
          return  t("workFlow.process");
        case 3:
          return  t("workFlow.end");
      }
    }
  };
  