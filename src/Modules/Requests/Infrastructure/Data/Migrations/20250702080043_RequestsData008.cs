﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Requests.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class RequestsData008 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Configuration",
                schema: "Requests",
                table: "TransitionRules",
                type: "nvarchar(max)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "{}",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldDefaultValue: "{}");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Configuration",
                schema: "Requests",
                table: "TransitionRules",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "{}",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldMaxLength: 1024,
                oldDefaultValue: "{}");
        }
    }
}
