using General.Domain;
using General.Infrastructure.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Folders.RenameFolder;

public class RenameFolderCommandHandler : IRequestHandler<RenameFolderCommand, Result>
{
    private readonly GeneralDbContext _dbContext;

    public RenameFolderCommandHandler(GeneralDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result> Handle(RenameFolderCommand request, CancellationToken cancellationToken)
    {
        var folder = await _dbContext.Folder
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (folder == null)
        {
            return Result.Failure(GeneralErrors.FolderNotFound(request.Id));
        }

        // Aynı parent klasör altında aynı isimde başka klasör var mı kontrol et
        var existingFolder = await _dbContext.Folder
            .FirstOrDefaultAsync(f =>
                f.ParentFolderId == folder.ParentFolderId &&
                f.Name == request.Name &&
                f.Id != request.Id,
                cancellationToken);

        if (existingFolder != null)
        {
            return Result.Failure(GeneralErrors.FolderNameExists(request.Name, folder.ParentFolderId));
        }

        folder.Update(request.Name);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
