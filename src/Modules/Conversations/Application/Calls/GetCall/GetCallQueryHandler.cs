using Conversations.Application.Abstractions;
using Conversations.Infrastructure.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.Calls.GetCall;

public class GetCallQueryHandler(
    IConversationDbContext dbContext
) : IRequestHandler<GetCallQuery, Result<CallDto>>
{
    private readonly IConversationDbContext _dbContext = dbContext;

    public async Task<Result<CallDto>> Handle(GetCallQuery request, CancellationToken cancellationToken)
    {
        var call = await _dbContext.Call
            .Include(x => x.Notes)
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (call == null)
        {
            return Result.Failure<CallDto>($"{request.Id} ID'li çağrı bulunamadı");
        }

        return Result.Success(new CallDto(
            call.Id,
            call.CallId,
            call.HistoryOfTheCall,
            call.Phone,
            call.CustomerId,
            call.UserId,
            call.Direction,
            call.Status,
            call.StartTime,
            call.EndTime,
            call.RecordingUrl,
            call.Transcription,
            call.AutoDialerId,
            call.Notes.Count,
            [.. call.Notes.Select(n => new CallNoteDto(
                n.Id,
                n.Content,
                n.InsertDate,
                n.InsertUserId))]));
    }
}
