using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;

namespace Conversations.Application.Calls.ListCalls;

public class ListCallsQueryHandler(
    IConversationDbContext dbContext,
    ISharedUserService userService,
    ISharedCustomerService customerService
) : IRequestHandler<ListCallsQuery, PagedResult<CallDto>>
{
    private readonly IConversationDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;
    private readonly ISharedCustomerService _customerService = customerService;

    public async Task<PagedResult<CallDto>> Handle(ListCallsQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Call
            .Include(x => x.Notes)
            .AsNoTracking()
            .AsQueryable();
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            query = query.Where(x => x.CallId.Contains(request.SearchTerm));
        }
        if (request.StartDate.HasValue)
        {
            query = query.Where(x => x.StartTime >= request.StartDate.Value);
        }
        if (request.EndDate.HasValue)
        {
            query = query.Where(x => x.StartTime <= request.EndDate.Value);
        }
        if (request.UserId.HasValue)
        {
            query = query.Where(x => x.UserId == request.UserId.Value);
        }
        if (request.CustomerId.HasValue)
        {
            query = query.Where(x => x.CustomerId == request.CustomerId.Value);
        }
        if (request.AutoDialerId.HasValue)
        {
            query = query.Where(x => x.AutoDialerId == request.AutoDialerId.Value);
        }
        if (request.IsArchive.HasValue)
        {
            query = query.Where(x => x.AutoDialer.IsArchive == request.IsArchive.Value);
        }
        if (request.Status.HasValue)
        {
            query = query.Where(x => x.Status == request.Status.Value);
        }
        if (request.Direction.HasValue)
        {
            query = query.Where(x => x.Direction == request.Direction.Value);
        }
        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await _dbContext.Call.CountAsync(cancellationToken);
        var items = await query
            .OrderByDescending(x => x.StartTime)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new CallDto(
                x.Id,
                x.CallId,
                x.HistoryOfTheCall,
                x.Phone,
                x.CustomerId,
                x.UserId,
                x.Direction,
                x.Status,
                x.StartTime,
                x.EndTime,
                x.RecordingUrl,
                x.Transcription,
                x.AutoDialerId,
                x.Notes.Count,
                null))
            .ToListAsync(cancellationToken);
        var userIds = items.Select(x => x.UserId).ToList();
        var users = (await _userService.GetUsersByIdsAsync(userIds))
            .ToDictionary(x => x.Id, x => x.Name + " " + x.Surname);
        items.ForEach(x => x.User = users.TryGetValue(x.UserId, out string value) ? value : "Bilinmeyen Kullanıcı");
        var customerIds = items.Where(x => x.CustomerId.HasValue).Select(x => x.CustomerId ?? Guid.Empty).ToList();
        var customers = (await _customerService.GetCustomerByIdsAsync(customerIds)).Value.ToDictionary(x => x.Id, x => x.Name + " " + x.Surname);
        items.ForEach(x => x.Customer = x.CustomerId.HasValue && customers.TryGetValue(x.CustomerId.Value, out var value) ? value : "Bilinmeyen Müşteri");
        return new PagedResult<CallDto>(items)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            FilteredCount = filteredCount,
            Count = totalCount,
        };
    }
}
