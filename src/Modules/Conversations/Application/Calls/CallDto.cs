using Conversations.Domain.Calls;

namespace Conversations.Application.Calls;

public record CallDto(
    Guid Id,
    string CallId,
    string HistoryOfTheCall,
    string Phone,
    Guid? CustomerId,
    Guid UserId,
    CallDirection Direction,
    CallStatus Status,
    DateTime StartTime,
    DateTime? EndTime,
    string RecordingUrl,
    string Transcription,
    Guid? AutoDialerId,
    int NoteCount,
    ICollection<CallNoteDto>? Notes = null)
{
    public string? User { get; set; }
    public string? Customer { get; set; }
}

public record CallNoteDto(
    Guid Id,
    string Content,
    DateTime InsertDate,
    Guid? InsertUserId);
